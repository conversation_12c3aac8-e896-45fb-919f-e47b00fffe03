package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicleModel.enums.DistanceUnit;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserNearByChargingStationsDto {
    @NotBlank
    String id ;

    @NotBlank
    Float latitude;

    @NotBlank
    Float longitude;

    @NotBlank
    Integer distance;

    @NotBlank
    DistanceUnit distanceUnit;
}